<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AiPPT智能生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, #00d4ff, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            color: #b0b0b0;
            font-size: 1.1rem;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #e0e0e0;
        }

        .input-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }

        .input-group input::placeholder {
            color: #888;
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #00d4ff, #ff6b6b);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-section {
            margin-top: 30px;
            display: none;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            z-index: 1;
        }

        .progress-line {
            position: absolute;
            top: 20px;
            left: 0;
            height: 2px;
            background: linear-gradient(45deg, #00d4ff, #ff6b6b);
            transition: width 0.5s ease;
            z-index: 2;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 3;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }

        .step.active .step-circle {
            background: linear-gradient(45deg, #00d4ff, #ff6b6b);
            animation: pulse 2s infinite;
        }

        .step.completed .step-circle {
            background: #4caf50;
        }

        .step-text {
            font-size: 12px;
            text-align: center;
            color: #b0b0b0;
        }

        .step.active .step-text {
            color: #ffffff;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(0, 212, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 212, 255, 0); }
        }

        .status-message {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-weight: 500;
        }

        .status-message.info {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: #00d4ff;
        }

        .status-message.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
        }

        .status-message.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        .result-section {
            margin-top: 30px;
            display: none;
        }

        .download-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #4caf50, #45a049);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ffffff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .progress-steps {
                flex-wrap: wrap;
                gap: 10px;
            }

            .step {
                flex: 1;
                min-width: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 AiPPT智能生成器</h1>
            <p>输入主题，一键生成专业PPT</p>
        </div>

        <div class="input-section">
            <div class="input-group">
                <label for="pptTitle">PPT主题</label>
                <input type="text" id="pptTitle" placeholder="请输入您的PPT主题，例如：人工智能发展趋势" maxlength="100">
            </div>
            <button class="generate-btn" id="generateBtn" onclick="startGeneration()">
                🚀 开始生成PPT
            </button>
        </div>

        <div class="progress-section" id="progressSection">
            <div class="progress-steps">
                <div class="progress-line" id="progressLine"></div>
                <div class="step" id="step1">
                    <div class="step-circle">1</div>
                    <div class="step-text">创建任务</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-circle">2</div>
                    <div class="step-text">生成大纲</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-circle">3</div>
                    <div class="step-text">生成内容</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-circle">4</div>
                    <div class="step-text">生成作品</div>
                </div>
            </div>
            <div class="status-message info" id="statusMessage">
                正在准备生成您的PPT...
            </div>
        </div>

        <div class="result-section" id="resultSection">
            <div class="status-message success">
                🎉 PPT生成成功！点击下方按钮下载您的专业PPT
            </div>
            <a class="download-btn" id="downloadBtn" href="#" target="_blank">
                📥 下载PPT文件
            </a>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTaskId = null;
        let currentStep = 0;
        const API_BASE_URL = 'https://joycode-api.jd.com';
        const API_TOKEN = 'HEv2ICldnMAOpBtUbqArHAKxdLjwuJAdxWwweS8j0KE=';

        // 控制台日志函数
        function logInfo(message, data = null) {
            console.log(`[AiPPT] ${message}`, data || '');
        }

        function logError(message, error = null) {
            console.error(`[AiPPT Error] ${message}`, error || '');
        }

        // 显示状态消息
        function showStatusMessage(message, type = 'info') {
            const statusElement = document.getElementById('statusMessage');
            statusElement.textContent = message;
            statusElement.className = `status-message ${type}`;
            logInfo(`状态更新: ${message}`);
        }

        // 更新进度步骤
        function updateProgress(step) {
            currentStep = step;
            const progressLine = document.getElementById('progressLine');
            const steps = document.querySelectorAll('.step');
            
            // 更新进度条
            const progressWidth = ((step - 1) / 3) * 100;
            progressLine.style.width = `${progressWidth}%`;
            
            // 更新步骤状态
            steps.forEach((stepElement, index) => {
                stepElement.classList.remove('active', 'completed');
                if (index + 1 < step) {
                    stepElement.classList.add('completed');
                } else if (index + 1 === step) {
                    stepElement.classList.add('active');
                }
            });
            
            logInfo(`进度更新: 步骤 ${step}/4`);
        }

        // API请求封装
        async function makeApiRequest(pluginId, params) {
            const url = `${API_BASE_URL}/api/saas/tool/v1/plugin/run/${pluginId}`;
            const requestData = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': API_TOKEN
                },
                body: JSON.stringify({ params })
            };

            logInfo(`发起API请求: Plugin ${pluginId}`, params);

            try {
                const response = await fetch(url, requestData);
                const data = await response.json();
                
                logInfo(`API响应: Plugin ${pluginId}`, data);
                
                if (data.code !== 200) {
                    throw new Error(`API错误: ${data.msg || '未知错误'}`);
                }
                
                return data;
            } catch (error) {
                logError(`API请求失败: Plugin ${pluginId}`, error);
                throw error;
            }
        }

        // 步骤1: 创建任务
        async function createTask(title) {
            updateProgress(1);
            showStatusMessage('正在创建PPT生成任务...');
            
            try {
                const response = await makeApiRequest(39273, { title });
                
                if (response.data && response.data.data && response.data.data.id) {
                    currentTaskId = response.data.data.id.toString();
                    logInfo(`任务创建成功, Task ID: ${currentTaskId}`);
                    return currentTaskId;
                } else {
                    throw new Error('创建任务失败：未获取到有效的任务ID');
                }
            } catch (error) {
                logError('创建任务失败', error);
                throw new Error(`创建任务失败: ${error.message}`);
            }
        }

        // 步骤2: 生成大纲
        async function generateOutline(taskId) {
            updateProgress(2);
            showStatusMessage('正在生成PPT大纲结构...');
            
            try {
                const response = await makeApiRequest(39284, { task_id: taskId });
                
                if (response.data && response.data.$$result) {
                    logInfo('大纲生成成功', response.data.$$result);
                    return response.data.$$result;
                } else {
                    throw new Error('生成大纲失败：未获取到大纲内容');
                }
            } catch (error) {
                logError('生成大纲失败', error);
                throw new Error(`生成大纲失败: ${error.message}`);
            }
        }

        // 步骤3: 生成内容
        async function generateContent(taskId) {
            updateProgress(3);
            showStatusMessage('正在生成PPT详细内容...');
            
            try {
                const response = await makeApiRequest(39285, { task_id: taskId });
                
                if (response.data && response.data.data) {
                    const contentId = response.data.data;
                    logInfo(`内容生成成功, Content ID: ${contentId}`);
                    return contentId;
                } else {
                    throw new Error('生成内容失败：未获取到内容ID');
                }
            } catch (error) {
                logError('生成内容失败', error);
                throw new Error(`生成内容失败: ${error.message}`);
            }
        }

        // 步骤4: 生成作品
        async function generatePPT(taskId) {
            updateProgress(4);
            showStatusMessage('正在生成最终PPT文件...');
            
            try {
                const response = await makeApiRequest(39288, { task_id: taskId });
                
                if (response.data && response.data.data && response.data.data.length > 0) {
                    const pptUrl = response.data.data[0];
                    logInfo(`PPT生成成功, 下载链接: ${pptUrl}`);
                    return pptUrl;
                } else {
                    throw new Error('生成PPT失败：未获取到下载链接');
                }
            } catch (error) {
                logError('生成PPT失败', error);
                throw new Error(`生成PPT失败: ${error.message}`);
            }
        }

        // 主生成流程
        async function startGeneration() {
            const titleInput = document.getElementById('pptTitle');
            const title = titleInput.value.trim();
            
            if (!title) {
                alert('请输入PPT主题');
                titleInput.focus();
                return;
            }

            // 重置状态
            currentTaskId = null;
            currentStep = 0;
            
            // 显示进度区域，隐藏结果区域
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            
            // 禁用生成按钮
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<span class="loading-spinner"></span>正在生成中...';
            
            logInfo('开始PPT生成流程', { title });

            try {
                // 步骤1: 创建任务
                const taskId = await createTask(title);
                
                // 步骤2: 生成大纲
                await generateOutline(taskId);
                
                // 步骤3: 生成内容
                await generateContent(taskId);
                
                // 步骤4: 生成PPT
                const pptUrl = await generatePPT(taskId);
                
                // 显示成功结果
                showGenerationSuccess(pptUrl);
                
            } catch (error) {
                logError('PPT生成流程失败', error);
                showGenerationError(error.message);
            } finally {
                // 恢复生成按钮
                generateBtn.disabled = false;
                generateBtn.innerHTML = '🚀 开始生成PPT';
            }
        }

        // 显示生成成功
        function showGenerationSuccess(pptUrl) {
            logInfo('PPT生成流程完成', { downloadUrl: pptUrl });
            
            // 更新进度为完成
            updateProgress(5);
            
            // 显示结果区域
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('downloadBtn').href = pptUrl;
            
            // 更新状态消息
            showStatusMessage('🎉 PPT生成完成！您可以下载使用了', 'success');
        }

        // 显示生成错误
        function showGenerationError(errorMessage) {
            logError('显示错误信息', errorMessage);
            showStatusMessage(`❌ 生成失败: ${errorMessage}`, 'error');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            logInfo('AiPPT应用初始化完成');
            
            // 为输入框添加回车键支持
            document.getElementById('pptTitle').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    startGeneration();
                }
            });
        });
    </script>
</body>
</html>