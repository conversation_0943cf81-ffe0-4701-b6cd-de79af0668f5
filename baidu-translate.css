/* 百度云机器翻译应用样式 */
/* 酷黑主题设计 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 100%);
    color: #f0f0f0;
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5rem;
    background: linear-gradient(90deg, #00c6ff, #0072ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.translation-box {
    background: rgba(30, 30, 30, 0.8);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.input-section, .output-section {
    margin-bottom: 25px;
}

.language-selector {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 10px;
}

.language-selector label {
    font-weight: 600;
    font-size: 1.1rem;
    color: #00c6ff;
}

.language-selector select {
    background: rgba(50, 50, 50, 0.8);
    color: #f0f0f0;
    border: 1px solid rgba(0, 198, 255, 0.3);
    border-radius: 8px;
    padding: 10px 15px;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
    flex: 1;
    max-width: 200px;
}

.language-selector select:focus {
    border-color: #00c6ff;
    box-shadow: 0 0 0 3px rgba(0, 198, 255, 0.2);
}

textarea {
    width: 100%;
    min-height: 150px;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(40, 40, 40, 0.8);
    color: #f0f0f0;
    font-size: 1.1rem;
    resize: vertical;
    transition: all 0.3s ease;
    outline: none;
}

textarea:focus {
    border-color: #00c6ff;
    box-shadow: 0 0 0 3px rgba(0, 198, 255, 0.2);
}

#source-text {
    margin-bottom: 10px;
}

.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 25px 0;
    flex-wrap: wrap;
    gap: 15px;
}

#translate-btn {
    background: linear-gradient(90deg, #00c6ff, #0072ff);
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 198, 255, 0.3);
}

#translate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 198, 255, 0.5);
}

#translate-btn:active {
    transform: translateY(0);
}

#translate-btn:disabled {
    background: linear-gradient(90deg, #555, #333);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.result-header label {
    font-weight: 600;
    font-size: 1.1rem;
    color: #00c6ff;
}

.loading {
    color: #00c6ff;
    font-style: italic;
}

.hidden {
    display: none;
}

.error {
    background: rgba(255, 50, 50, 0.2);
    border: 1px solid rgba(255, 50, 50, 0.5);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    color: #ff6b6b;
    text-align: center;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .translation-box {
        padding: 20px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    #translate-btn {
        padding: 15px;
        font-size: 1.2rem;
    }
    
    .language-selector {
        flex-wrap: wrap;
    }
    
    .language-selector select {
        max-width: none;
    }
}