<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: white;
        }

        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .score, .high-score {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 10px;
            min-width: 120px;
        }

        #gameCanvas {
            border: 3px solid #fff;
            border-radius: 10px;
            background-color: #2c3e50;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .controls {
            margin-top: 20px;
            font-size: 1.1em;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(3, 60px);
            grid-template-rows: repeat(3, 60px);
            gap: 5px;
            justify-content: center;
            margin: 20px auto;
            max-width: 200px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        #upBtn { grid-column: 2; grid-row: 1; }
        #leftBtn { grid-column: 1; grid-row: 2; }
        #rightBtn { grid-column: 3; grid-row: 2; }
        #downBtn { grid-column: 2; grid-row: 3; }

        .game-buttons {
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .game-over {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .game-over-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
        }

        .game-over h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
            color: #ff6b6b;
        }

        .game-over p {
            font-size: 1.3em;
            margin-bottom: 30px;
        }

        @media (max-width: 768px) {
            .game-container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            #gameCanvas {
                width: 300px;
                height: 300px;
            }
            
            .game-info {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 贪吃蛇游戏</h1>
        
        <div class="game-info">
            <div class="score">
                得分: <span id="score">0</span>
            </div>
            <div class="high-score">
                最高分: <span id="highScore">0</span>
            </div>
        </div>

        <canvas id="gameCanvas" width="400" height="400"></canvas>

        <div class="controls">
            <p>使用方向键或点击按钮控制蛇的移动</p>
            <div class="control-buttons">
                <button class="control-btn" id="upBtn">↑</button>
                <button class="control-btn" id="leftBtn">←</button>
                <button class="control-btn" id="rightBtn">→</button>
                <button class="control-btn" id="downBtn">↓</button>
            </div>
        </div>

        <div class="game-buttons">
            <button class="btn" onclick="startGame()">开始游戏</button>
            <button class="btn" onclick="pauseGame()">暂停/继续</button>
            <button class="btn" onclick="resetGame()">重新开始</button>
        </div>
    </div>

    <div class="game-over" id="gameOverScreen">
        <div class="game-over-content">
            <h2>游戏结束!</h2>
            <p>你的得分: <span id="finalScore">0</span></p>
            <p id="newRecord" style="display: none; color: #ffd700;">🎉 新纪录!</p>
            <button class="btn" onclick="resetGame()">再来一局</button>
        </div>
    </div>

    <script>
        // 游戏变量
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const highScoreElement = document.getElementById('highScore');
        const gameOverScreen = document.getElementById('gameOverScreen');
        const finalScoreElement = document.getElementById('finalScore');
        const newRecordElement = document.getElementById('newRecord');

        // 游戏配置
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        // 游戏状态
        let snake = [
            {x: 10, y: 10}
        ];
        let food = {};
        let dx = 0;
        let dy = 0;
        let score = 0;
        let highScore = localStorage.getItem('snakeHighScore') || 0;
        let gameRunning = false;
        let gamePaused = false;
        let gameLoop;

        // 初始化游戏
        function initGame() {
            highScoreElement.textContent = highScore;
            generateFood();
            drawGame();
        }

        // 生成食物
        function generateFood() {
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
            
            // 确保食物不会生成在蛇身上
            for (let segment of snake) {
                if (segment.x === food.x && segment.y === food.y) {
                    generateFood();
                    return;
                }
            }
        }

        // 绘制游戏
        function drawGame() {
            // 清空画布
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制蛇
            ctx.fillStyle = '#27ae60';
            for (let segment of snake) {
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
            }

            // 绘制蛇头（不同颜色）
            if (snake.length > 0) {
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(snake[0].x * gridSize, snake[0].y * gridSize, gridSize - 2, gridSize - 2);
            }

            // 绘制食物
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.arc(
                food.x * gridSize + gridSize / 2,
                food.y * gridSize + gridSize / 2,
                gridSize / 2 - 1,
                0,
                2 * Math.PI
            );
            ctx.fill();
        }

        // 移动蛇
        function moveSnake() {
            if (!gameRunning || gamePaused) return;
            
            // 如果蛇还没有开始移动，不执行移动逻辑
            if (dx === 0 && dy === 0) return;

            const head = {x: snake[0].x + dx, y: snake[0].y + dy};

            // 检查碰撞
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                gameOver();
                return;
            }

            // 检查是否撞到自己
            for (let segment of snake) {
                if (head.x === segment.x && head.y === segment.y) {
                    gameOver();
                    return;
                }
            }

            snake.unshift(head);

            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                score += 10;
                scoreElement.textContent = score;
                generateFood();
            } else {
                snake.pop();
            }

            drawGame();
        }

        // 开始游戏
        function startGame() {
            if (gameRunning) return;
            
            gameRunning = true;
            gamePaused = false;
            gameOverScreen.style.display = 'none';
            
            // 重置蛇的位置和方向
            snake = [{x: 10, y: 10}];
            dx = 0;
            dy = 0;
            score = 0;
            scoreElement.textContent = score;
            
            generateFood();
            drawGame();
            
            gameLoop = setInterval(moveSnake, 150);
        }

        // 暂停游戏
        function pauseGame() {
            if (!gameRunning) return;
            
            gamePaused = !gamePaused;
        }

        // 重新开始游戏
        function resetGame() {
            gameRunning = false;
            gamePaused = false;
            clearInterval(gameLoop);
            gameOverScreen.style.display = 'none';
            startGame();
        }

        // 游戏结束
        function gameOver() {
            gameRunning = false;
            clearInterval(gameLoop);
            
            finalScoreElement.textContent = score;
            
            // 检查是否创造新纪录
            if (score > highScore) {
                highScore = score;
                highScoreElement.textContent = highScore;
                localStorage.setItem('snakeHighScore', highScore);
                newRecordElement.style.display = 'block';
            } else {
                newRecordElement.style.display = 'none';
            }
            
            gameOverScreen.style.display = 'flex';
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning || gamePaused) return;

            switch(e.key) {
                case 'ArrowUp':
                    if (dy !== 1) {
                        dx = 0;
                        dy = -1;
                    }
                    break;
                case 'ArrowDown':
                    if (dy !== -1) {
                        dx = 0;
                        dy = 1;
                    }
                    break;
                case 'ArrowLeft':
                    if (dx !== 1) {
                        dx = -1;
                        dy = 0;
                    }
                    break;
                case 'ArrowRight':
                    if (dx !== -1) {
                        dx = 1;
                        dy = 0;
                    }
                    break;
                case ' ':
                    e.preventDefault();
                    pauseGame();
                    break;
            }
        });

        // 按钮控制
        document.getElementById('upBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (dy !== 1) {
                dx = 0;
                dy = -1;
            }
        });

        document.getElementById('downBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (dy !== -1) {
                dx = 0;
                dy = 1;
            }
        });

        document.getElementById('leftBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (dx !== 1) {
                dx = -1;
                dy = 0;
            }
        });

        document.getElementById('rightBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (dx !== -1) {
                dx = 1;
                dy = 0;
            }
        });

        // 初始化游戏
        initGame();
    </script>
</body>
</html>