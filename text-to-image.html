<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文生图应用 - AI图像生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: #a0a0a0;
            font-size: 1.1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .input-section, .output-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #e0e0e0;
        }

        textarea, input, select {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        textarea:focus, input:focus, select:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.15);
        }

        textarea {
            min-height: 120px;
            resize: vertical;
        }

        .slider-group {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .slider {
            flex: 1;
            -webkit-appearance: none;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            outline: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: #667eea;
            border-radius: 50%;
            cursor: pointer;
        }

        .slider-value {
            min-width: 40px;
            text-align: center;
            color: #a0a0a0;
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .generate-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .image-container {
            width: 100%;
            min-height: 400px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .placeholder {
            color: #666;
            font-size: 1.2rem;
            text-align: center;
        }

        .generated-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .loading {
            display: none;
            text-align: center;
            color: #667eea;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: rgba(255, 59, 48, 0.1);
            border: 1px solid rgba(255, 59, 48, 0.3);
            color: #ff3b30;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }

        .success-message {
            background: rgba(52, 199, 89, 0.1);
            border: 1px solid rgba(52, 199, 89, 0.3);
            color: #34c759;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }

        .download-btn {
            background: rgba(52, 199, 89, 0.2);
            border: 1px solid #34c759;
            color: #34c759;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 15px;
            display: none;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: rgba(52, 199, 89, 0.3);
        }

        .api-key-section {
            margin-bottom: 20px;
        }

        .api-key-input {
            font-family: monospace;
        }

        .console-output {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
            color: #a0a0a0;
        }

        .console-line {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .console-info { color: #667eea; }
        .console-success { color: #34c759; }
        .console-error { color: #ff3b30; }
        .console-warning { color: #ff9500; }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI 文生图生成器</h1>
            <p>输入文字描述，AI为您生成精美图像</p>
        </div>

        <div class="main-content">
            <div class="input-section">
                <h2>参数设置</h2>
                
                <div class="api-key-section">
                    <label for="apiKey">API密钥 (SiliconFlow)</label>
                    <input type="password" id="apiKey" class="api-key-input" placeholder="输入您的SiliconFlow API密钥">
                </div>

                <div class="form-group">
                    <label for="prompt">图像描述</label>
                    <textarea id="prompt" placeholder="例如：一只可爱的橘猫在樱花树下打盹，日系动漫风格，柔和光线，唯美场景..."></textarea>
                </div>

                <div class="form-group">
                    <label for="model">选择模型</label>
                    <select id="model">
                        <option value="black-forest-labs/FLUX.1-schnell">FLUX.1-schnell (快速)</option>
                        <option value="black-forest-labs/FLUX.1-dev">FLUX.1-dev (开发版)</option>
                        <option value="stabilityai/stable-diffusion-xl-base-1.0">SDXL 1.0</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>图像尺寸</label>
                    <select id="imageSize">
                        <option value="1024x1024">1024x1024 (正方形)</option>
                        <option value="1152x896">1152x896 (横向)</option>
                        <option value="896x1152">896x1152 (纵向)</option>
                        <option value="1344x768">1344x768 (宽屏)</option>
                        <option value="768x1344">768x1344 (竖屏)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>推理步数: <span class="slider-value" id="stepsValue">20</span></label>
                    <div class="slider-group">
                        <input type="range" id="numSteps" class="slider" min="1" max="50" value="20">
                    </div>
                </div>

                <div class="form-group">
                    <label>引导系数: <span class="slider-value" id="guidanceValue">7.5</span></label>
                    <div class="slider-group">
                        <input type="range" id="guidanceScale" class="slider" min="1" max="20" step="0.1" value="7.5">
                    </div>
                </div>

                <button class="generate-btn" onclick="generateImage()">生成图像</button>

                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>
            </div>

            <div class="output-section">
                <h2>生成结果</h2>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>正在生成图像，请稍候...</p>
                </div>

                <div class="image-container" id="imageContainer">
                    <div class="placeholder">输入描述并点击生成按钮开始创作</div>
                </div>

                <button class="download-btn" id="downloadBtn" onclick="downloadImage()">下载图像</button>
            </div>
        </div>

        <div class="console-output" id="consoleOutput">
            <div class="console-line console-info">系统初始化完成，等待用户操作...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentImageUrl = null;

        // 控制台输出函数
        function logToConsole(message, type = 'info') {
            const console = document.getElementById('consoleOutput');
            const line = document.createElement('div');
            line.className = `console-line console-${type}`;
            const timestamp = new Date().toLocaleTimeString();
            line.textContent = `[${timestamp}] ${message}`;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }

        // 更新滑块值显示
        document.getElementById('numSteps').addEventListener('input', function(e) {
            document.getElementById('stepsValue').textContent = e.target.value;
        });

        document.getElementById('guidanceScale').addEventListener('input', function(e) {
            document.getElementById('guidanceValue').textContent = e.target.value;
        });

        // 显示消息
        function showMessage(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.style.display = 'block';
            setTimeout(() => {
                element.style.display = 'none';
            }, 5000);
        }

        // 生成图像函数
        async function generateImage() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const prompt = document.getElementById('prompt').value.trim();
            const model = document.getElementById('model').value;
            const imageSize = document.getElementById('imageSize').value;
            const numSteps = document.getElementById('numSteps').value;
            const guidanceScale = document.getElementById('guidanceScale').value;

            // 验证输入
            if (!apiKey) {
                showMessage('errorMessage', '请输入API密钥');
                logToConsole('错误：API密钥为空', 'error');
                return;
            }

            if (!prompt) {
                showMessage('errorMessage', '请输入图像描述');
                logToConsole('错误：图像描述为空', 'error');
                return;
            }

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('downloadBtn').style.display = 'none';
            document.querySelector('.generate-btn').disabled = true;

            logToConsole('开始生成图像...', 'info');
            logToConsole(`模型: ${model}`, 'info');
            logToConsole(`描述: ${prompt}`, 'info');

            try {
                const [width, height] = imageSize.split('x').map(Number);
                
                const response = await fetch('https://api.siliconflow.cn/v1/images/generations', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: model,
                        prompt: prompt,
                        width: width,
                        height: height,
                        num_inference_steps: parseInt(numSteps),
                        guidance_scale: parseFloat(guidanceScale)
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error?.message || `HTTP错误: ${response.status}`);
                }

                const data = await response.json();
                
                if (data.images && data.images[0] && data.images[0].url) {
                    currentImageUrl = data.images[0].url;
                    
                    // 显示生成的图像
                    const imageContainer = document.getElementById('imageContainer');
                    imageContainer.innerHTML = `<img src="${currentImageUrl}" alt="生成的图像" class="generated-image">`;
                    
                    // 显示成功消息和下载按钮
                    showMessage('successMessage', '图像生成成功！');
                    document.getElementById('downloadBtn').style.display = 'inline-block';
                    
                    logToConsole('图像生成成功', 'success');
                    logToConsole(`图像URL: ${currentImageUrl}`, 'success');
                } else {
                    throw new Error('API返回格式错误');
                }

            } catch (error) {
                console.error('生成图像时出错:', error);
                showMessage('errorMessage', `生成失败: ${error.message}`);
                logToConsole(`生成失败: ${error.message}`, 'error');
                
                // 重置图像容器
                document.getElementById('imageContainer').innerHTML = 
                    '<div class="placeholder">生成失败，请重试</div>';
            } finally {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                document.querySelector('.generate-btn').disabled = false;
            }
        }

        // 下载图像函数
        async function downloadImage() {
            if (!currentImageUrl) {
                showMessage('errorMessage', '没有可下载的图像');
                return;
            }

            try {
                logToConsole('开始下载图像...', 'info');
                
                const response = await fetch(currentImageUrl);
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `ai-generated-${Date.now()}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                logToConsole('图像下载完成', 'success');
                showMessage('successMessage', '图像下载成功！');
            } catch (error) {
                logToConsole(`下载失败: ${error.message}`, 'error');
                showMessage('errorMessage', `下载失败: ${error.message}`);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('文生图应用已加载完成', 'info');
            logToConsole('提示：请先在左侧输入您的SiliconFlow API密钥', 'warning');
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                generateImage();
            }
        });
    </script>
</body>
</html>