# 文生图

# 文生图 Service Interface API Documentation

文生图（Text2Image）基于文心大模型，可以根据用户输入的文本，自动创作不限定风格的图，为内容创作者提供灵感和高质量配图。

## 使用说明
1. 调用接口1提交请求，获得任务ID(task_id)
2. 轮询调用接口2查询结果，sub_task_status=SUCCESS时，可获得图片地址数据

## 1. 提交请求

输入图片基本参数信息，包含文本描述，尺寸、数量、分辨率等，创建 AI 作画任务，获得任务ID(task_id)。

### Interface URL

`POST /api/saas/tool/v1/plugin/run/39041`

**Fetch Request Configuration：**
```javascript
{
  method: "POST",
  url: "https://joycode-api.jd.com/api/saas/tool/v1/plugin/run/39041",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "lwrs4h6ng7CqcTFAnl7E3C6jlw6fMAp9K4N9nh2zMkA="
  },
  body: {
    params: {
      prompt: "", // String, 必填, 生图的文本描述。支持中文、英文、日常标点符号（~！@#$%&*()-+[];:'',./）。不支持特殊符号，限制 200 字
      width: 0, // Integer, 必填, 图片宽度，支持：512x512、640x360、360x640、1024x1024、1280x720、720x1280、2048x2048、2560x1440、1440x2560、3840x2160、2160x3840
      height: 0, // Integer, 必填, 图片高度，支持：512x512、640x360、360x640、1024x1024、1280x720、720x1280、2048x2048、2560x1440、1440x2560、3840x2160、2160x3840
      image_num: 0, // Number, 不必填, 生成图片数量，默认一张，支持生成 1-8 张
      image: "", // String, 不必填, 参考图，需 base64 编码，大小不超过 10M，最短边至少 15px，最长边最大 8192px，支持jpg/jpeg/png/bmp 格式。优先级：image > url > pdf_file，当image 字段存在时，url、pdf_file 字段失效，和url/pdf_file 三选一
      url: "", // String, 不必填, 参考图完整 url，url 长度不超过 1024 字节，url 对应的图片需 base64 编码，大小不超过 10M，最短边至少 15px，最长边最大8192px，支持 jpg/jpeg/png/bmp 格式。优先级：image > url > pdf_file，当image 字段存在时，url 字段失效请注意关闭 URL 防盗链，和image/pdf_file 三选一
      pdf_file: "", // String, 不必填, 参考图 PDF 文件，base64 编码，大小不超过10M，最短边至少 15px，最长边最大 8192px 。优先级：image > url > pdf_file，当image 字段存在时，url、pdf_file 字段失效，和image/url 三选一
      pdf_file_num: "", // String, 不必填, 需要识别的 PDF 文件的对应页码，当pdf_file 参数有效时，识别传入页码的对应页面内容，若不传入，则默认识别第 1 页
      change_degree: 0, // Integer, 不必填, 当 image、url或 pdf_file 字段存在时，为必需项参考图影响因子，支持 1-10 内；数值越大参考图影响越大
      text_content: "", // String, 不必填, 1~50个字符，支持英文、数字及常用特殊字符。若不传该参数则默认为Text-to-Image-内容ID-AI，示例：Text-to-Image-131870381_0_finaI.png-Al；若传该参数，则相应水印内容自动添加至Text-to-Image后方，示例：Text-to-Image-ABCD-131870381_0_finaI.png-Al
      task_time_out: 0 // Integer, 不必填, 仅支持数字，自定义超时时间（单位：s）：10, 30, 60, 90, 120, 300, 600, 900
    }
  }
}
```

**Fetch Response Format：**
```javascript
{
  code: 200, // number, 响应状态码
  msg: "", // string, 响应消息
  data: {
    log_id: "", // String, 
    data: { // Object, 
      task_id: "", // String, 用于查询结果
      primary_task_id: "" // String, 
    }
  }
}
```

## 2. 查询结果

用于在任务创建后，查看图片生成状态。待图片生成完毕，通过查询接口即可查看生成图片的地址链接。

### Interface URL

`POST /api/saas/tool/v1/plugin/run/39044`

**Fetch Request Configuration：**
```javascript
{
  method: "POST",
  url: "https://joycode-api.jd.com/api/saas/tool/v1/plugin/run/39044",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "lwrs4h6ng7CqcTFAnl7E3C6jlw6fMAp9K4N9nh2zMkA="
  },
  body: {
    params: {
      task_id: "" // String, 必填, 从提交请求的提交接口的返回值中获取，可使用task_id 查询总任务
    }
  }
}
```

**Fetch Response Format：**
```javascript
{
  code: 200, // number, 响应状态码
  msg: "", // string, 响应消息
  data: {
    log_id: "", // String, 
    data: { // Object, 
      task_progress_detail: 0, // Integer, 
      task_progress: 0, // Integer, 
      task_status: "", // String, 
      task_id: 0, // Integer, 
      sub_task_result_list: [ // Array, 
        { // object, 数组元素
          sub_task_error_code: 0, // Integer, 
          sub_task_progress_detail: 0, // Integer, 
          sub_task_progress: 0, // Integer, 
          final_image_list: [ // Array, 图片列表
            { // object, 数组元素
              img_url: "" // String, 图片地址 
            }
          ],
          sub_task_id: "", // String, 
          sub_task_status: "" // String,  状态包括RUNNING/SUCCESS
        }
      ]
    }
  }
}
```

