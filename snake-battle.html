<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇对战游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: white;
        }

        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .mode-selection {
            margin-bottom: 20px;
        }

        .mode-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .mode-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .mode-btn.active {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .score-panel {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 10px;
            min-width: 120px;
        }

        .player-score {
            color: #2ecc71;
        }

        .ai-score {
            color: #e74c3c;
        }

        #gameCanvas {
            border: 3px solid #fff;
            border-radius: 10px;
            background-color: #2c3e50;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .controls {
            margin-top: 20px;
            font-size: 1.1em;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(3, 60px);
            grid-template-rows: repeat(3, 60px);
            gap: 5px;
            justify-content: center;
            margin: 20px auto;
            max-width: 200px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        #upBtn { grid-column: 2; grid-row: 1; }
        #leftBtn { grid-column: 1; grid-row: 2; }
        #rightBtn { grid-column: 3; grid-row: 2; }
        #downBtn { grid-column: 2; grid-row: 3; }

        .game-buttons {
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .game-over {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .game-over-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
        }

        .game-over h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
            color: #ff6b6b;
        }

        .game-over p {
            font-size: 1.3em;
            margin-bottom: 30px;
        }

        .winner {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .winner.player-win {
            color: #2ecc71;
        }

        .winner.ai-win {
            color: #e74c3c;
        }

        .ai-difficulty {
            margin-bottom: 20px;
        }

        .difficulty-btn {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 15px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .difficulty-btn:hover {
            transform: translateY(-1px);
        }

        .difficulty-btn.active {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }

        @media (max-width: 768px) {
            .game-container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            #gameCanvas {
                width: 300px;
                height: 300px;
            }
            
            .game-info {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 贪吃蛇对战游戏</h1>
        
        <div class="mode-selection">
            <button class="mode-btn active" id="singleMode" onclick="setGameMode('single')">单人模式</button>
            <button class="mode-btn" id="battleMode" onclick="setGameMode('battle')">人机对战</button>
        </div>

        <div class="ai-difficulty" id="difficultyPanel" style="display: none;">
            <p>AI难度:</p>
            <button class="difficulty-btn active" onclick="setDifficulty('easy')">简单</button>
            <button class="difficulty-btn" onclick="setDifficulty('medium')">中等</button>
            <button class="difficulty-btn" onclick="setDifficulty('hard')">困难</button>
        </div>
        
        <div class="game-info">
            <div class="score-panel">
                <div class="player-score">玩家: <span id="playerScore">0</span></div>
            </div>
            <div class="score-panel">
                <div class="high-score">最高分: <span id="highScore">0</span></div>
            </div>
            <div class="score-panel" id="aiScorePanel" style="display: none;">
                <div class="ai-score">AI: <span id="aiScore">0</span></div>
            </div>
        </div>

        <canvas id="gameCanvas" width="400" height="400"></canvas>

        <div class="controls">
            <p>使用方向键或点击按钮控制蛇的移动</p>
            <div class="control-buttons">
                <button class="control-btn" id="upBtn">↑</button>
                <button class="control-btn" id="leftBtn">←</button>
                <button class="control-btn" id="rightBtn">→</button>
                <button class="control-btn" id="downBtn">↓</button>
            </div>
        </div>

        <div class="game-buttons">
            <button class="btn" onclick="startGame()">开始游戏</button>
            <button class="btn" onclick="pauseGame()">暂停/继续</button>
            <button class="btn" onclick="resetGame()">重新开始</button>
        </div>
    </div>

    <div class="game-over" id="gameOverScreen">
        <div class="game-over-content">
            <h2>游戏结束!</h2>
            <div id="winnerText" class="winner"></div>
            <p>玩家得分: <span id="finalPlayerScore">0</span></p>
            <p id="finalAIScore" style="display: none;">AI得分: <span id="finalAIScoreValue">0</span></p>
            <p id="newRecord" style="display: none; color: #ffd700;">🎉 新纪录!</p>
            <button class="btn" onclick="resetGame()">再来一局</button>
        </div>
    </div>

    <script>
        // 游戏变量
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const playerScoreElement = document.getElementById('playerScore');
        const aiScoreElement = document.getElementById('aiScore');
        const highScoreElement = document.getElementById('highScore');
        const gameOverScreen = document.getElementById('gameOverScreen');
        const finalPlayerScoreElement = document.getElementById('finalPlayerScore');
        const finalAIScoreElement = document.getElementById('finalAIScoreValue');
        const newRecordElement = document.getElementById('newRecord');
        const winnerTextElement = document.getElementById('winnerText');

        // 游戏配置
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        // 游戏状态
        let gameMode = 'single'; // 'single' or 'battle'
        let aiDifficulty = 'easy'; // 'easy', 'medium', 'hard'
        let playerSnake = [{x: 5, y: 10}];
        let aiSnake = [{x: 15, y: 10}];
        let food = {};
        let playerDx = 0, playerDy = 0;
        let aiDx = 0, aiDy = 0;
        let playerScore = 0;
        let aiScore = 0;
        let highScore = localStorage.getItem('snakeBattleHighScore') || 0;
        let gameRunning = false;
        let gamePaused = false;
        let gameLoop;

        // AI移动策略
        const aiStrategies = {
            easy: { lookAhead: 1, randomness: 0.3, speed: 200 },
            medium: { lookAhead: 2, randomness: 0.1, speed: 150 },
            hard: { lookAhead: 3, randomness: 0.05, speed: 120 }
        };

        // 设置游戏模式
        function setGameMode(mode) {
            gameMode = mode;
            document.getElementById('singleMode').classList.toggle('active', mode === 'single');
            document.getElementById('battleMode').classList.toggle('active', mode === 'battle');
            document.getElementById('difficultyPanel').style.display = mode === 'battle' ? 'block' : 'none';
            document.getElementById('aiScorePanel').style.display = mode === 'battle' ? 'block' : 'none';
            
            if (gameRunning) {
                resetGame();
            }
        }

        // 设置AI难度
        function setDifficulty(difficulty) {
            aiDifficulty = difficulty;
            document.querySelectorAll('.difficulty-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        // 初始化游戏
        function initGame() {
            highScoreElement.textContent = highScore;
            generateFood();
            drawGame();
        }

        // 生成食物
        function generateFood() {
            do {
                food = {
                    x: Math.floor(Math.random() * tileCount),
                    y: Math.floor(Math.random() * tileCount)
                };
            } while (isPositionOccupied(food.x, food.y));
        }

        // 检查位置是否被占用
        function isPositionOccupied(x, y) {
            // 检查玩家蛇
            for (let segment of playerSnake) {
                if (segment.x === x && segment.y === y) return true;
            }
            
            // 检查AI蛇（如果在对战模式）
            if (gameMode === 'battle') {
                for (let segment of aiSnake) {
                    if (segment.x === x && segment.y === y) return true;
                }
            }
            
            return false;
        }

        // 绘制游戏
        function drawGame() {
            // 清空画布
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制玩家蛇
            ctx.fillStyle = '#27ae60';
            for (let i = 1; i < playerSnake.length; i++) {
                ctx.fillRect(playerSnake[i].x * gridSize, playerSnake[i].y * gridSize, gridSize - 2, gridSize - 2);
            }
            
            // 绘制玩家蛇头
            if (playerSnake.length > 0) {
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(playerSnake[0].x * gridSize, playerSnake[0].y * gridSize, gridSize - 2, gridSize - 2);
            }

            // 绘制AI蛇（如果在对战模式）
            if (gameMode === 'battle') {
                ctx.fillStyle = '#c0392b';
                for (let i = 1; i < aiSnake.length; i++) {
                    ctx.fillRect(aiSnake[i].x * gridSize, aiSnake[i].y * gridSize, gridSize - 2, gridSize - 2);
                }
                
                // 绘制AI蛇头
                if (aiSnake.length > 0) {
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(aiSnake[0].x * gridSize, aiSnake[0].y * gridSize, gridSize - 2, gridSize - 2);
                }
            }

            // 绘制食物
            ctx.fillStyle = '#f39c12';
            ctx.beginPath();
            ctx.arc(
                food.x * gridSize + gridSize / 2,
                food.y * gridSize + gridSize / 2,
                gridSize / 2 - 1,
                0,
                2 * Math.PI
            );
            ctx.fill();
        }

        // AI寻路算法（简化版A*）
        function getAIDirection() {
            const strategy = aiStrategies[aiDifficulty];
            
            // 添加随机性
            if (Math.random() < strategy.randomness) {
                const directions = [
                    {dx: 0, dy: -1}, // 上
                    {dx: 0, dy: 1},  // 下
                    {dx: -1, dy: 0}, // 左
                    {dx: 1, dy: 0}   // 右
                ];
                
                const validDirections = directions.filter(dir => {
                    // 不能反向移动
                    if (dir.dx === -aiDx && dir.dy === -aiDy) return false;
                    
                    const newHead = {
                        x: aiSnake[0].x + dir.dx,
                        y: aiSnake[0].y + dir.dy
                    };
                    
                    return !isCollision(newHead, aiSnake) && !isWallCollision(newHead);
                });
                
                if (validDirections.length > 0) {
                    const randomDir = validDirections[Math.floor(Math.random() * validDirections.length)];
                    return randomDir;
                }
            }

            // 计算到食物的最佳路径
            const head = aiSnake[0];
            const directions = [
                {dx: 0, dy: -1, score: 0}, // 上
                {dx: 0, dy: 1, score: 0},  // 下
                {dx: -1, dy: 0, score: 0}, // 左
                {dx: 1, dy: 0, score: 0}   // 右
            ];

            // 评估每个方向
            directions.forEach(dir => {
                // 不能反向移动
                if (dir.dx === -aiDx && dir.dy === -aiDy) {
                    dir.score = -1000;
                    return;
                }

                const newHead = {
                    x: head.x + dir.dx,
                    y: head.y + dir.dy
                };

                // 检查碰撞
                if (isWallCollision(newHead) || isCollision(newHead, aiSnake) || 
                    (gameMode === 'battle' && isCollision(newHead, playerSnake))) {
                    dir.score = -1000;
                    return;
                }

                // 计算到食物的距离
                const distanceToFood = Math.abs(newHead.x - food.x) + Math.abs(newHead.y - food.y);
                dir.score = -distanceToFood;

                // 避免陷入死胡同
                const spaceAround = countSpaceAround(newHead);
                dir.score += spaceAround * 10;
            });

            // 选择最佳方向
            const validDirections = directions.filter(dir => dir.score > -1000);
            if (validDirections.length === 0) {
                // 如果没有安全方向，随机选择一个
                return directions[0];
            }

            validDirections.sort((a, b) => b.score - a.score);
            return validDirections[0];
        }

        // 计算周围空间
        function countSpaceAround(pos) {
            let count = 0;
            const directions = [{dx: 0, dy: -1}, {dx: 0, dy: 1}, {dx: -1, dy: 0}, {dx: 1, dy: 0}];
            
            directions.forEach(dir => {
                const newPos = {x: pos.x + dir.dx, y: pos.y + dir.dy};
                if (!isWallCollision(newPos) && !isPositionOccupied(newPos.x, newPos.y)) {
                    count++;
                }
            });
            
            return count;
        }

        // 检查墙壁碰撞
        function isWallCollision(pos) {
            return pos.x < 0 || pos.x >= tileCount || pos.y < 0 || pos.y >= tileCount;
        }

        // 检查蛇身碰撞
        function isCollision(pos, snake) {
            for (let segment of snake) {
                if (pos.x === segment.x && pos.y === segment.y) {
                    return true;
                }
            }
            return false;
        }

        // 移动蛇
        function moveSnakes() {
            if (!gameRunning || gamePaused) return;

            let gameEnded = false;

            // 移动玩家蛇
            if (playerDx !== 0 || playerDy !== 0) {
                const playerHead = {x: playerSnake[0].x + playerDx, y: playerSnake[0].y + playerDy};

                // 检查玩家碰撞
                if (isWallCollision(playerHead) || isCollision(playerHead, playerSnake) ||
                    (gameMode === 'battle' && isCollision(playerHead, aiSnake))) {
                    gameOver('ai');
                    return;
                }

                playerSnake.unshift(playerHead);

                // 检查玩家是否吃到食物
                if (playerHead.x === food.x && playerHead.y === food.y) {
                    playerScore += 10;
                    playerScoreElement.textContent = playerScore;
                    generateFood();
                } else {
                    playerSnake.pop();
                }
            }

            // 移动AI蛇（如果在对战模式）
            if (gameMode === 'battle') {
                const aiDirection = getAIDirection();
                aiDx = aiDirection.dx;
                aiDy = aiDirection.dy;

                if (aiDx !== 0 || aiDy !== 0) {
                    const aiHead = {x: aiSnake[0].x + aiDx, y: aiSnake[0].y + aiDy};

                    // 检查AI碰撞
                    if (isWallCollision(aiHead) || isCollision(aiHead, aiSnake) || isCollision(aiHead, playerSnake)) {
                        gameOver('player');
                        return;
                    }

                    aiSnake.unshift(aiHead);

                    // 检查AI是否吃到食物
                    if (aiHead.x === food.x && aiHead.y === food.y) {
                        aiScore += 10;
                        aiScoreElement.textContent = aiScore;
                        generateFood();
                    } else {
                        aiSnake.pop();
                    }
                }
            }

            drawGame();
        }

        // 开始游戏
        function startGame() {
            if (gameRunning) return;
            
            gameRunning = true;
            gamePaused = false;
            gameOverScreen.style.display = 'none';
            
            // 重置蛇的位置和方向
            playerSnake = [{x: 5, y: 10}];
            aiSnake = [{x: 15, y: 10}];
            playerDx = 0;
            playerDy = 0;
            aiDx = 0;
            aiDy = 0;
            playerScore = 0;
            aiScore = 0;
            playerScoreElement.textContent = playerScore;
            aiScoreElement.textContent = aiScore;
            
            generateFood();
            drawGame();
            
            const speed = gameMode === 'battle' ? aiStrategies[aiDifficulty].speed : 150;
            gameLoop = setInterval(moveSnakes, speed);
        }

        // 暂停游戏
        function pauseGame() {
            if (!gameRunning) return;
            gamePaused = !gamePaused;
        }

        // 重新开始游戏
        function resetGame() {
            gameRunning = false;
            gamePaused = false;
            clearInterval(gameLoop);
            gameOverScreen.style.display = 'none';
            startGame();
        }

        // 游戏结束
        function gameOver(winner) {
            gameRunning = false;
            clearInterval(gameLoop);
            
            finalPlayerScoreElement.textContent = playerScore;
            
            if (gameMode === 'battle') {
                document.getElementById('finalAIScore').style.display = 'block';
                finalAIScoreElement.textContent = aiScore;
                
                if (winner === 'player') {
                    winnerTextElement.textContent = '🎉 玩家获胜!';
                    winnerTextElement.className = 'winner player-win';
                } else {
                    winnerTextElement.textContent = '🤖 AI获胜!';
                    winnerTextElement.className = 'winner ai-win';
                }
            } else {
                document.getElementById('finalAIScore').style.display = 'none';
                winnerTextElement.textContent = '';
            }
            
            // 检查是否创造新纪录
            const currentScore = gameMode === 'battle' ? Math.max(playerScore, aiScore) : playerScore;
            if (currentScore > highScore) {
                highScore = currentScore;
                highScoreElement.textContent = highScore;
                localStorage.setItem('snakeBattleHighScore', highScore);
                newRecordElement.style.display = 'block';
            } else {
                newRecordElement.style.display = 'none';
            }
            
            gameOverScreen.style.display = 'flex';
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning || gamePaused) return;

            switch(e.key) {
                case 'ArrowUp':
                    if (playerDy !== 1) {
                        playerDx = 0;
                        playerDy = -1;
                    }
                    break;
                case 'ArrowDown':
                    if (playerDy !== -1) {
                        playerDx = 0;
                        playerDy = 1;
                    }
                    break;
                case 'ArrowLeft':
                    if (playerDx !== 1) {
                        playerDx = -1;
                        playerDy = 0;
                    }
                    break;
                case 'ArrowRight':
                    if (playerDx !== -1) {
                        playerDx = 1;
                        playerDy = 0;
                    }
                    break;
                case ' ':
                    e.preventDefault();
                    pauseGame();
                    break;
            }
        });

        // 按钮控制
        document.getElementById('upBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (playerDy !== 1) {
                playerDx = 0;
                playerDy = -1;
            }
        });

        document.getElementById('downBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (playerDy !== -1) {
                playerDx = 0;
                playerDy = 1;
            }
        });

        document.getElementById('leftBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (playerDx !== 1) {
                playerDx = -1;
                playerDy = 0;
            }
        });

        document.getElementById('rightBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (playerDx !== -1) {
                playerDx = 1;
                playerDy = 0;
            }
        });

        // 初始化游戏
        initGame();
    </script>
</body>
</html>