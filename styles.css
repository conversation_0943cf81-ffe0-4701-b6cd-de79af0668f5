/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", Arial, sans-serif;
    background-color: #f4f4f4;
    color: #333;
}

.container {
    width: 1200px;
    margin: 0 auto;
}

/* 顶部导航栏 */
.top-bar {
    background-color: #e3e4e5;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
}

.top-links {
    list-style: none;
    display: flex;
    justify-content: flex-end;
}

.top-links li {
    margin-left: 15px;
}

.top-links a {
    text-decoration: none;
    color: #999;
}

.top-links a:hover {
    color: #c81623;
}

/* 头部区域 */
.header {
    background-color: #fff;
    padding: 20px 0;
    height: 80px;
}

.logo h1 a {
    float: left;
    width: 190px;
    height: 50px;
    background-color: #c81623;
    color: #fff;
    font-size: 24px;
    text-align: center;
    line-height: 50px;
    text-decoration: none;
}

.search {
    float: left;
    width: 500px;
    margin: 10px 50px;
}

.search input {
    width: 400px;
    height: 35px;
    border: 2px solid #c81623;
    padding: 0 10px;
    font-size: 14px;
    float: left;
}

.search button {
    width: 80px;
    height: 35px;
    background-color: #c81623;
    color: #fff;
    border: none;
    font-size: 16px;
    cursor: pointer;
}

.cart {
    float: right;
    width: 120px;
    height: 35px;
    margin-top: 10px;
    background-color: #fff;
    border: 1px solid #e3e4e5;
    text-align: center;
    line-height: 35px;
}

.cart a {
    text-decoration: none;
    color: #c81623;
    font-weight: bold;
}

/* 导航菜单 */
.main-nav {
    background-color: #c81623;
    height: 40px;
    line-height: 40px;
}

.category-menu {
    float: left;
    width: 200px;
    background-color: #b1191a;
    text-align: center;
    color: #fff;
    font-weight: bold;
}

.nav-links {
    list-style: none;
    display: flex;
}

.nav-links li {
    margin-right: 30px;
}

.nav-links a {
    text-decoration: none;
    color: #fff;
    font-size: 16px;
}

.nav-links a:hover {
    color: #ffd700;
}

/* 主要内容区域 */
.main-content {
    margin-top: 20px;
}

/* 轮播图区域 */
.banner {
    height: 300px;
    background-color: #fff;
    margin-bottom: 20px;
}

.banner-slider img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 促销模块 */
.promo-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.promo-item {
    width: 32%;
    height: 150px;
    background-color: #fff;
    text-align: center;
    padding: 20px;
    box-shadow: 0 0 5px rgba(0,0,0,0.1);
}

.promo-item h3 {
    color: #c81623;
    margin-bottom: 10px;
}

.promo-item p {
    margin-bottom: 10px;
    color: #666;
}

.time-down {
    font-size: 20px;
    font-weight: bold;
    color: #c81623;
}

.time-down span {
    background-color: #333;
    color: #fff;
    padding: 2px 5px;
    margin: 0 2px;
}

/* 商品楼层 */
.product-floor {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
}

.product-floor h2 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #c81623;
    border-left: 3px solid #c81623;
    padding-left: 10px;
}

.product-list {
    display: flex;
    justify-content: space-between;
}

.product-item {
    width: 32%;
    text-align: center;
    border: 1px solid #e3e4e5;
    padding: 10px;
}

.product-item img {
    width: 100%;
    height: 200px;
    object-fit: contain;
}

.product-name {
    margin: 10px 0;
    height: 40px;
    overflow: hidden;
}

.product-price {
    color: #c81623;
    font-weight: bold;
    font-size: 18px;
}

/* 底部信息 */
.footer {
    background-color: #fff;
    padding: 20px 0;
    margin-top: 20px;
}

.footer-links {
    text-align: center;
    margin-bottom: 20px;
}

.footer-links ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.footer-links li {
    margin: 0 10px;
}

.footer-links a {
    text-decoration: none;
    color: #666;
    font-size: 12px;
}

.footer-links a:hover {
    color: #c81623;
}

.copyright {
    text-align: center;
    color: #999;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        width: 960px;
    }
    
    .search {
        width: 400px;
        margin: 10px 30px;
    }
    
    .search input {
        width: 320px;
    }
}

@media (max-width: 960px) {
    .container {
        width: 720px;
    }
    
    .search {
        width: 300px;
        margin: 10px 20px;
    }
    
    .search input {
        width: 220px;
    }
    
    .nav-links li {
        margin-right: 15px;
    }
    
    .nav-links a {
        font-size: 14px;
    }
    
    .product-list {
        flex-wrap: wrap;
    }
    
    .product-item {
        width: 48%;
        margin-bottom: 10px;
    }
}

@media (max-width: 720px) {
    .container {
        width: 100%;
        padding: 0 10px;
    }
    
    .top-links {
        justify-content: center;
    }
    
    .logo h1 a {
        width: 120px;
        height: 40px;
        line-height: 40px;
        font-size: 20px;
    }
    
    .search {
        width: 200px;
        margin: 10px 15px;
    }
    
    .search input {
        width: 140px;
    }
    
    .cart {
        width: 100px;
        margin-top: 15px;
    }
    
    .category-menu {
        width: 120px;
        font-size: 14px;
    }
    
    .nav-links li {
        margin-right: 10px;
    }
    
    .nav-links a {
        font-size: 12px;
    }
    
    .promo-section {
        flex-wrap: wrap;
    }
    
    .promo-item {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .product-item {
        width: 100%;
        margin-bottom: 15px;
    }
}