<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AiPPT智能生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, #00d4ff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            color: #cccccc;
            font-size: 1.1rem;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: #ffffff;
            font-weight: 500;
        }

        .input-group input {
            width: 100%;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }

        .input-group input::placeholder {
            color: #888888;
        }

        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #00d4ff, #ff00ff);
            border: none;
            border-radius: 10px;
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-section {
            margin: 30px 0;
            display: none;
        }

        .progress-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid transparent;
        }

        .progress-item.pending {
            border-left-color: #666666;
        }

        .progress-item.processing {
            border-left-color: #00d4ff;
            animation: pulse 2s infinite;
        }

        .progress-item.completed {
            border-left-color: #00ff88;
        }

        .progress-item.error {
            border-left-color: #ff4444;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .progress-icon {
            width: 24px;
            height: 24px;
            margin-right: 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .progress-icon.pending {
            background: #666666;
        }

        .progress-icon.processing {
            background: #00d4ff;
            animation: spin 1s linear infinite;
        }

        .progress-icon.completed {
            background: #00ff88;
        }

        .progress-icon.error {
            background: #ff4444;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            margin-top: 30px;
            display: none;
        }

        .result-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .result-title {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #00d4ff;
        }

        .outline-content {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }

        .download-btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(45deg, #00ff88, #00d4ff);
            color: #ffffff;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.4);
        }

        .error-message {
            background: rgba(255, 68, 68, 0.1);
            border: 1px solid #ff4444;
            color: #ff4444;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            display: none;
        }

        .log-section {
            margin-top: 30px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
            opacity: 0.8;
        }

        .log-entry.info {
            color: #00d4ff;
        }

        .log-entry.success {
            color: #00ff88;
        }

        .log-entry.error {
            color: #ff4444;
        }

        .log-entry.warning {
            color: #ffaa00;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 AiPPT智能生成器</h1>
            <p>输入主题，一键生成专业PPT</p>
        </div>

        <div class="input-section">
            <div class="input-group">
                <label for="ppt-title">PPT主题</label>
                <input type="text" id="ppt-title" placeholder="请输入PPT主题，例如：人工智能发展趋势" />
            </div>
            <button class="generate-btn" id="generate-btn" onclick="generatePPT()">
                🚀 开始生成PPT
            </button>
        </div>

        <div class="progress-section" id="progress-section">
            <div class="progress-item pending" id="step-1">
                <div class="progress-icon pending" id="icon-1">1</div>
                <div>
                    <div><strong>创建任务</strong></div>
                    <div style="font-size: 0.9rem; color: #cccccc;">初始化PPT生成任务</div>
                </div>
            </div>
            <div class="progress-item pending" id="step-2">
                <div class="progress-icon pending" id="icon-2">2</div>
                <div>
                    <div><strong>生成大纲</strong></div>
                    <div style="font-size: 0.9rem; color: #cccccc;">创建PPT结构化大纲</div>
                </div>
            </div>
            <div class="progress-item pending" id="step-3">
                <div class="progress-icon pending" id="icon-3">3</div>
                <div>
                    <div><strong>生成内容</strong></div>
                    <div style="font-size: 0.9rem; color: #cccccc;">根据大纲生成详细内容</div>
                </div>
            </div>
            <div class="progress-item pending" id="step-4">
                <div class="progress-icon pending" id="icon-4">4</div>
                <div>
                    <div><strong>内容处理</strong></div>
                    <div style="font-size: 0.9rem; color: #cccccc;">处理和优化PPT内容</div>
                </div>
            </div>
            <div class="progress-item pending" id="step-5">
                <div class="progress-icon pending" id="icon-5">5</div>
                <div>
                    <div><strong>生成PPT树</strong></div>
                    <div style="font-size: 0.9rem; color: #cccccc;">构建PPT结构树</div>
                </div>
            </div>
            <div class="progress-item pending" id="step-6">
                <div class="progress-icon pending" id="icon-6">6</div>
                <div>
                    <div><strong>生成作品</strong></div>
                    <div style="font-size: 0.9rem; color: #cccccc;">生成最终PPT文件</div>
                </div>
            </div>
        </div>

        <div class="result-section" id="result-section">
            <div class="result-card">
                <div class="result-title">📋 生成大纲预览</div>
                <div class="outline-content" id="outline-content"></div>
                
                <div class="result-title">📥 下载PPT</div>
                <div id="download-links"></div>
            </div>
        </div>

        <div class="error-message" id="error-message"></div>

        <div class="log-section">
            <div style="margin-bottom: 10px; font-weight: bold; color: #00d4ff;">📋 执行日志</div>
            <div id="log-container"></div>
        </div>
    </div>

    <script>
        // API配置
        const API_CONFIG = {
            baseUrl: 'https://joycode-api.jd.com',
            authorization: 'HEv2ICldnMAOpBtUbqArHAKxdLjwuJAdxWwweS8j0KE=',
            endpoints: {
                createTask: '/api/saas/tool/v1/plugin/run/39273',
                generateOutline: '/api/saas/tool/v1/plugin/run/39284',
                generateContent: '/api/saas/tool/v1/plugin/run/39285',
                queryContentResult: '/api/saas/tool/v1/plugin/run/39286',
                generatePPTTree: '/api/saas/tool/v1/plugin/run/39287',
                generateWork: '/api/saas/tool/v1/plugin/run/39288'
            }
        };

        // 全局变量
        let currentTaskId = null;
        let currentTicket = null;
        let isGenerating = false;

        // 日志记录函数
        function addLog(message, type = 'info') {
            console.log(`[AiPPT] ${message}`);
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新进度状态
        function updateProgress(stepNumber, status) {
            const step = document.getElementById(`step-${stepNumber}`);
            const icon = document.getElementById(`icon-${stepNumber}`);
            
            // 移除所有状态类
            step.className = 'progress-item';
            icon.className = 'progress-icon';
            
            // 添加新状态类
            step.classList.add(status);
            icon.classList.add(status);
            
            // 更新图标内容
            if (status === 'completed') {
                icon.textContent = '✓';
            } else if (status === 'error') {
                icon.textContent = '✗';
            } else if (status === 'processing') {
                icon.textContent = '⟳';
            }
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            addLog(`错误: ${message}`, 'error');
        }

        // 隐藏错误信息
        function hideError() {
            document.getElementById('error-message').style.display = 'none';
        }

        // API请求封装
        async function makeAPIRequest(endpoint, params) {
            const url = API_CONFIG.baseUrl + endpoint;
            const requestBody = {
                params: params
            };

            addLog(`发送请求到: ${endpoint}`, 'info');
            addLog(`请求参数: ${JSON.stringify(params)}`, 'info');

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': API_CONFIG.authorization
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                addLog(`响应数据: ${JSON.stringify(data)}`, 'info');
                
                if (data.code !== 200) {
                    throw new Error(`API错误: ${data.msg || '未知错误'}`);
                }

                return data;
            } catch (error) {
                addLog(`请求失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 步骤1: 创建任务
        async function createTask(title) {
            updateProgress(1, 'processing');
            addLog('开始创建PPT生成任务...', 'info');

            try {
                const response = await makeAPIRequest(API_CONFIG.endpoints.createTask, {
                    title: title
                });

                if (response.data && response.data.data && response.data.data.id) {
                    currentTaskId = response.data.data.id.toString();
                    updateProgress(1, 'completed');
                    addLog(`任务创建成功，任务ID: ${currentTaskId}`, 'success');
                    return currentTaskId;
                } else {
                    throw new Error('创建任务响应格式错误');
                }
            } catch (error) {
                updateProgress(1, 'error');
                throw new Error(`创建任务失败: ${error.message}`);
            }
        }

        // 步骤2: 生成大纲
        async function generateOutline(taskId) {
            updateProgress(2, 'processing');
            addLog('开始生成PPT大纲...', 'info');

            try {
                const response = await makeAPIRequest(API_CONFIG.endpoints.generateOutline, {
                    task_id: taskId
                });

                if (response.data && response.data.$$result) {
                    updateProgress(2, 'completed');
                    addLog('PPT大纲生成成功', 'success');
                    
                    // 显示大纲内容
                    document.getElementById('outline-content').textContent = response.data.$$result;
                    document.getElementById('result-section').style.display = 'block';
                    
                    return response.data.$$result;
                } else {
                    throw new Error('生成大纲响应格式错误');
                }
            } catch (error) {
                updateProgress(2, 'error');
                throw new Error(`生成大纲失败: ${error.message}`);
            }
        }

        // 步骤3: 生成内容
        async function generateContent(taskId) {
            updateProgress(3, 'processing');
            addLog('开始生成PPT内容...', 'info');

            try {
                const response = await makeAPIRequest(API_CONFIG.endpoints.generateContent, {
                    task_id: taskId
                });

                if (response.data && response.data.data) {
                    currentTicket = response.data.data;
                    updateProgress(3, 'completed');
                    addLog(`内容生成任务提交成功，票据ID: ${currentTicket}`, 'success');
                    return currentTicket;
                } else {
                    throw new Error('生成内容响应格式错误');
                }
            } catch (error) {
                updateProgress(3, 'error');
                throw new Error(`生成内容失败: ${error.message}`);
            }
        }

        // 步骤4: 查询内容结果（轮询）
        async function queryContentResult(taskId, ticket) {
            updateProgress(4, 'processing');
            addLog('开始查询内容生成状态...', 'info');

            const maxRetries = 60; // 最大重试次数（增加到60次）
            const retryInterval = 5000; // 重试间隔（毫秒，增加到5秒）

            for (let i = 0; i < maxRetries; i++) {
                try {
                    addLog(`第 ${i + 1} 次查询内容状态...`, 'info');
                    
                    const response = await makeAPIRequest(API_CONFIG.endpoints.queryContentResult, {
                        task_id: taskId,
                        ticket: ticket
                    });

                    if (response.data && response.data.data) {
                        const status = response.data.data.status;
                        const content = response.data.data.content;

                        addLog(`内容状态: ${status}`, 'info');

                        // 处理各种可能的状态值
                        if (status === 1 || status === 2 || status === "1" || status === "2" || 
                            (typeof status === 'string' && status.toLowerCase() === 'completed') ||
                            (content && content.length > 0)) { // 多种完成状态的判断
                            updateProgress(4, 'completed');
                            addLog('内容生成完成', 'success');
                            return content || '内容生成完成';
                        } else if (status === -1 || status === "-1" || 
                                 (typeof status === 'string' && status.toLowerCase() === 'failed')) { // 失败状态
                            throw new Error('内容生成失败');
                        } else if (status === 0 || status === "0" || 
                                 (typeof status === 'string' && status.toLowerCase() === 'processing')) { // 处理中状态
                            addLog('内容正在生成中...', 'info');
                        } else {
                            addLog(`未知状态: ${status}，继续等待...`, 'warning');
                        }
                        // 其他状态继续轮询
                    } else {
                        addLog('响应数据格式异常，继续重试...', 'warning');
                    }

                    // 等待后重试
                    if (i < maxRetries - 1) {
                        addLog(`等待 ${retryInterval/1000} 秒后重试...`, 'warning');
                        await new Promise(resolve => setTimeout(resolve, retryInterval));
                    }
                } catch (error) {
                    if (i === maxRetries - 1) {
                        updateProgress(4, 'error');
                        throw new Error(`查询内容结果失败: ${error.message}`);
                    }
                    addLog(`查询失败，将重试: ${error.message}`, 'warning');
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            }

            updateProgress(4, 'error');
            addLog('内容生成超时，但将继续尝试后续步骤...', 'warning');
            return '内容生成超时，但继续执行';
        }

        // 步骤5: 生成PPT树
        async function generatePPTTree(taskId) {
            updateProgress(5, 'processing');
            addLog('开始生成PPT结构树...', 'info');

            try {
                const response = await makeAPIRequest(API_CONFIG.endpoints.generatePPTTree, {
                    task_id: taskId
                });

                if (response.data && response.data.$$result) {
                    updateProgress(5, 'completed');
                    addLog('PPT结构树生成成功', 'success');
                    return response.data.$$result;
                } else {
                    throw new Error('生成PPT树响应格式错误');
                }
            } catch (error) {
                updateProgress(5, 'error');
                throw new Error(`生成PPT树失败: ${error.message}`);
            }
        }

        // 步骤6: 生成作品
        async function generateWork(taskId) {
            updateProgress(6, 'processing');
            addLog('开始生成最终PPT文件...', 'info');

            try {
                const response = await makeAPIRequest(API_CONFIG.endpoints.generateWork, {
                    task_id: taskId
                });

                if (response.data && response.data.data && Array.isArray(response.data.data)) {
                    updateProgress(6, 'completed');
                    addLog('PPT文件生成成功', 'success');
                    
                    // 显示下载链接
                    const downloadLinks = document.getElementById('download-links');
                    downloadLinks.innerHTML = '';
                    
                    response.data.data.forEach((url, index) => {
                        const link = document.createElement('a');
                        link.href = url;
                        link.className = 'download-btn';
                        link.textContent = `📥 下载PPT文件 ${index + 1}`;
                        link.target = '_blank';
                        link.style.marginRight = '10px';
                        link.style.marginBottom = '10px';
                        downloadLinks.appendChild(link);
                    });
                    
                    return response.data.data;
                } else {
                    throw new Error('生成作品响应格式错误');
                }
            } catch (error) {
                updateProgress(6, 'error');
                throw new Error(`生成作品失败: ${error.message}`);
            }
        }

        // 主要生成函数
        async function generatePPT() {
            if (isGenerating) {
                addLog('正在生成中，请勿重复点击', 'warning');
                return;
            }

            const title = document.getElementById('ppt-title').value.trim();
            if (!title) {
                showError('请输入PPT主题');
                return;
            }

            // 重置状态
            isGenerating = true;
            hideError();
            document.getElementById('generate-btn').disabled = true;
            document.getElementById('generate-btn').textContent = '🔄 生成中...';
            document.getElementById('progress-section').style.display = 'block';
            document.getElementById('result-section').style.display = 'none';

            // 重置所有进度状态
            for (let i = 1; i <= 6; i++) {
                updateProgress(i, 'pending');
                document.getElementById(`icon-${i}`).textContent = i;
            }

            addLog(`开始生成PPT，主题: ${title}`, 'info');

            try {
                // 执行完整流程，每步都有独立的错误处理
                let taskId = null;
                let ticket = null;

                // 步骤1: 创建任务
                try {
                    taskId = await createTask(title);
                } catch (error) {
                    addLog(`步骤1失败: ${error.message}`, 'error');
                    throw error; // 任务创建失败则无法继续
                }

                // 步骤2: 生成大纲
                try {
                    await generateOutline(taskId);
                } catch (error) {
                    addLog(`步骤2失败: ${error.message}，继续执行后续步骤`, 'warning');
                    updateProgress(2, 'error');
                }

                // 步骤3: 生成内容
                try {
                    ticket = await generateContent(taskId);
                } catch (error) {
                    addLog(`步骤3失败: ${error.message}，继续执行后续步骤`, 'warning');
                    updateProgress(3, 'error');
                }

                // 步骤4: 查询内容结果
                if (ticket) {
                    try {
                        await queryContentResult(taskId, ticket);
                    } catch (error) {
                        addLog(`步骤4失败: ${error.message}，继续执行后续步骤`, 'warning');
                        updateProgress(4, 'error');
                    }
                } else {
                    addLog('跳过步骤4（无票据ID）', 'warning');
                    updateProgress(4, 'error');
                }

                // 步骤5: 生成PPT树
                try {
                    await generatePPTTree(taskId);
                } catch (error) {
                    addLog(`步骤5失败: ${error.message}，继续执行后续步骤`, 'warning');
                    updateProgress(5, 'error');
                }

                // 步骤6: 生成作品
                try {
                    await generateWork(taskId);
                    addLog('🎉 PPT生成完成！', 'success');
                } catch (error) {
                    addLog(`步骤6失败: ${error.message}`, 'error');
                    updateProgress(6, 'error');
                    showError(`最终生成失败: ${error.message}`);
                }
                
            } catch (error) {
                showError(error.message);
                addLog(`生成失败: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                isGenerating = false;
                document.getElementById('generate-btn').disabled = false;
                document.getElementById('generate-btn').textContent = '🚀 开始生成PPT';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('AiPPT智能生成器已就绪', 'success');
            addLog('请输入PPT主题开始生成', 'info');
            
            // 回车键触发生成
            document.getElementById('ppt-title').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    generatePPT();
                }
            });
        });
    </script>
</body>
</html>