# 文生图应用需求文档

## 应用概述
开发一个基于文生图API的Web应用，用户可以通过输入文本描述来生成相应的图片。

## 功能需求

### 1. 用户界面
- 文本输入区域：用户可以输入图片描述（支持中英文，限制200字）
- 尺寸选择器：提供预设的图片尺寸选项
  - 512x512
  - 640x360
  - 360x640
  - 1024x1024
  - 1280x720
  - 720x1280
- 生成按钮：提交图片生成请求
- 状态显示区域：显示生成进度
- 结果展示区域：显示生成的图片

### 2. 核心功能流程
1. 用户输入文本描述并选择图片尺寸
2. 点击生成按钮后，应用调用API提交请求
3. 获取任务ID后，应用定期轮询查询结果
4. 在等待期间，向用户显示进度信息
5. 当图片生成完成后，在结果区域显示生成的图片

### 3. 技术要求
- 使用HTML、CSS、JavaScript技术栈（不使用module模式）
- 采用酷黑主题，界面交互友好
- 在浏览器控制台输出流程信息，方便调试
- 代码包含详细注释，便于理解
- 处理接口异常情况，并给出用户友好的提示信息

### 4. 错误处理
- 网络错误提示
- API返回错误信息展示
- 输入验证（如文本长度限制）
- 超时处理

## 非功能需求
- 响应式设计，适配不同屏幕尺寸
- 加载状态提示，提升用户体验
- 代码结构清晰，易于维护