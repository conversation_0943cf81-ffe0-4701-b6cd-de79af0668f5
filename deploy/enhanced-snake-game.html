<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版贪吃蛇游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: white;
        }

        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .game-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            font-size: 1.1em;
            font-weight: bold;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 15px;
            border-radius: 10px;
        }

        .difficulty-selector {
            margin-bottom: 20px;
        }

        .difficulty-selector label {
            font-size: 1.1em;
            margin-right: 10px;
        }

        .difficulty-selector select {
            padding: 8px 15px;
            border-radius: 10px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1em;
            cursor: pointer;
        }

        #gameCanvas {
            border: 3px solid #fff;
            border-radius: 10px;
            background-color: #2c3e50;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .controls {
            margin-top: 20px;
            font-size: 1.1em;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(3, 60px);
            grid-template-rows: repeat(3, 60px);
            gap: 5px;
            justify-content: center;
            margin: 20px auto;
            max-width: 200px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        #upBtn { grid-column: 2; grid-row: 1; }
        #leftBtn { grid-column: 1; grid-row: 2; }
        #rightBtn { grid-column: 3; grid-row: 2; }
        #downBtn { grid-column: 2; grid-row: 3; }

        .game-buttons {
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .game-over {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .game-over-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
        }

        .game-over h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
            color: #ff6b6b;
        }

        .game-over p {
            font-size: 1.3em;
            margin-bottom: 30px;
        }

        .power-up-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 215, 0, 0.9);
            color: #333;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
            display: none;
        }

        @media (max-width: 768px) {
            .game-container {
                padding: 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            #gameCanvas {
                width: 300px;
                height: 300px;
            }
            
            .game-stats {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🐍 增强版贪吃蛇</h1>
        
        <div class="difficulty-selector">
            <label for="difficulty">难度等级:</label>
            <select id="difficulty">
                <option value="easy">简单 (慢速)</option>
                <option value="medium" selected>中等 (中速)</option>
                <option value="hard">困难 (快速)</option>
                <option value="expert">专家 (极速)</option>
            </select>
        </div>
        
        <div class="game-stats">
            <div class="stat-item">
                得分: <span id="score">0</span>
            </div>
            <div class="stat-item">
                最高分: <span id="highScore">0</span>
            </div>
            <div class="stat-item">
                长度: <span id="length">1</span>
            </div>
            <div class="stat-item">
                等级: <span id="level">1</span>
            </div>
        </div>

        <div style="position: relative;">
            <canvas id="gameCanvas" width="400" height="400"></canvas>
            <div class="power-up-indicator" id="powerUpIndicator">
                ⚡ 加速中! <span id="powerUpTimer">5</span>s
            </div>
        </div>

        <div class="controls">
            <p>使用方向键或点击按钮控制蛇的移动 | 空格键暂停</p>
            <div class="control-buttons">
                <button class="control-btn" id="upBtn">↑</button>
                <button class="control-btn" id="leftBtn">←</button>
                <button class="control-btn" id="rightBtn">→</button>
                <button class="control-btn" id="downBtn">↓</button>
            </div>
        </div>

        <div class="game-buttons">
            <button class="btn" onclick="startGame()">开始游戏</button>
            <button class="btn" onclick="pauseGame()">暂停/继续</button>
            <button class="btn" onclick="resetGame()">重新开始</button>
        </div>
    </div>

    <div class="game-over" id="gameOverScreen">
        <div class="game-over-content">
            <h2>游戏结束!</h2>
            <p>你的得分: <span id="finalScore">0</span></p>
            <p>蛇的长度: <span id="finalLength">1</span></p>
            <p>达到等级: <span id="finalLevel">1</span></p>
            <p id="newRecord" style="display: none; color: #ffd700;">🎉 新纪录!</p>
            <button class="btn" onclick="resetGame()">再来一局</button>
        </div>
    </div>

    <script>
        // 游戏变量
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const highScoreElement = document.getElementById('highScore');
        const lengthElement = document.getElementById('length');
        const levelElement = document.getElementById('level');
        const gameOverScreen = document.getElementById('gameOverScreen');
        const finalScoreElement = document.getElementById('finalScore');
        const finalLengthElement = document.getElementById('finalLength');
        const finalLevelElement = document.getElementById('finalLevel');
        const newRecordElement = document.getElementById('newRecord');
        const difficultySelect = document.getElementById('difficulty');
        const powerUpIndicator = document.getElementById('powerUpIndicator');
        const powerUpTimer = document.getElementById('powerUpTimer');

        // 游戏配置
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        // 难度设置
        const difficultySettings = {
            easy: { speed: 200, scoreMultiplier: 1 },
            medium: { speed: 150, scoreMultiplier: 1.5 },
            hard: { speed: 100, scoreMultiplier: 2 },
            expert: { speed: 70, scoreMultiplier: 3 }
        };

        // 游戏状态
        let snake = [{ x: 10, y: 10 }];
        let food = {};
        let specialFood = null;
        let dx = 0;
        let dy = 0;
        let score = 0;
        let level = 1;
        let highScore = localStorage.getItem('enhancedSnakeHighScore') || 0;
        let gameRunning = false;
        let gamePaused = false;
        let gameLoop;
        let currentSpeed = 150;
        let scoreMultiplier = 1.5;
        let powerUpActive = false;
        let powerUpTimeLeft = 0;
        let powerUpInterval;

        // 音效（使用Web Audio API创建简单音效）
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        function playSound(frequency, duration, type = 'sine') {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.value = frequency;
            oscillator.type = type;
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        }

        // 初始化游戏
        function initGame() {
            console.log('🎮 初始化增强版贪吃蛇游戏');
            highScoreElement.textContent = highScore;
            updateDifficulty();
            generateFood();
            generateSpecialFood();
            drawGame();
        }

        // 更新难度
        function updateDifficulty() {
            const difficulty = difficultySelect.value;
            const settings = difficultySettings[difficulty];
            currentSpeed = settings.speed;
            scoreMultiplier = settings.scoreMultiplier;
            console.log(`🎯 难度设置: ${difficulty}, 速度: ${currentSpeed}ms, 分数倍数: ${scoreMultiplier}`);
        }

        // 生成普通食物
        function generateFood() {
            do {
                food = {
                    x: Math.floor(Math.random() * tileCount),
                    y: Math.floor(Math.random() * tileCount)
                };
            } while (isPositionOccupied(food.x, food.y));
            console.log(`🍎 生成普通食物: (${food.x}, ${food.y})`);
        }

        // 生成特殊食物
        function generateSpecialFood() {
            // 20% 概率生成特殊食物
            if (Math.random() < 0.2 && snake.length > 3) {
                do {
                    specialFood = {
                        x: Math.floor(Math.random() * tileCount),
                        y: Math.floor(Math.random() * tileCount),
                        type: Math.random() < 0.5 ? 'speed' : 'bonus' // 加速或奖励分数
                    };
                } while (isPositionOccupied(specialFood.x, specialFood.y));
                console.log(`⭐ 生成特殊食物: ${specialFood.type} at (${specialFood.x}, ${specialFood.y})`);
            }
        }

        // 检查位置是否被占用
        function isPositionOccupied(x, y) {
            // 检查蛇身
            for (let segment of snake) {
                if (segment.x === x && segment.y === y) {
                    return true;
                }
            }
            // 检查普通食物
            if (food.x === x && food.y === y) {
                return true;
            }
            // 检查特殊食物
            if (specialFood && specialFood.x === x && specialFood.y === y) {
                return true;
            }
            return false;
        }

        // 绘制游戏
        function drawGame() {
            // 清空画布
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制网格（可选）
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            for (let i = 0; i <= tileCount; i++) {
                ctx.beginPath();
                ctx.moveTo(i * gridSize, 0);
                ctx.lineTo(i * gridSize, canvas.height);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(0, i * gridSize);
                ctx.lineTo(canvas.width, i * gridSize);
                ctx.stroke();
            }

            // 绘制蛇身
            ctx.fillStyle = powerUpActive ? '#f39c12' : '#27ae60';
            for (let i = 1; i < snake.length; i++) {
                ctx.fillRect(snake[i].x * gridSize + 1, snake[i].y * gridSize + 1, gridSize - 2, gridSize - 2);
            }

            // 绘制蛇头（不同颜色和样式）
            if (snake.length > 0) {
                ctx.fillStyle = powerUpActive ? '#e67e22' : '#2ecc71';
                ctx.fillRect(snake[0].x * gridSize + 1, snake[0].y * gridSize + 1, gridSize - 2, gridSize - 2);
                
                // 绘制眼睛
                ctx.fillStyle = '#fff';
                const headX = snake[0].x * gridSize;
                const headY = snake[0].y * gridSize;
                ctx.fillRect(headX + 5, headY + 5, 3, 3);
                ctx.fillRect(headX + 12, headY + 5, 3, 3);
            }

            // 绘制普通食物
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.arc(
                food.x * gridSize + gridSize / 2,
                food.y * gridSize + gridSize / 2,
                gridSize / 2 - 2,
                0,
                2 * Math.PI
            );
            ctx.fill();

            // 绘制特殊食物
            if (specialFood) {
                if (specialFood.type === 'speed') {
                    // 闪电形状的加速食物
                    ctx.fillStyle = '#f1c40f';
                    ctx.font = `${gridSize - 4}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.fillText('⚡', 
                        specialFood.x * gridSize + gridSize / 2, 
                        specialFood.y * gridSize + gridSize / 2 + 6
                    );
                } else {
                    // 星形的奖励食物
                    ctx.fillStyle = '#9b59b6';
                    ctx.font = `${gridSize - 4}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.fillText('⭐', 
                        specialFood.x * gridSize + gridSize / 2, 
                        specialFood.y * gridSize + gridSize / 2 + 6
                    );
                }
            }
        }

        // 移动蛇
        function moveSnake() {
            if (!gameRunning || gamePaused) return;
            
            // 如果蛇还没有开始移动，不执行移动逻辑
            if (dx === 0 && dy === 0) return;

            const head = { x: snake[0].x + dx, y: snake[0].y + dy };

            // 检查边界碰撞
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                console.log('💥 撞墙了！游戏结束');
                gameOver();
                return;
            }

            // 检查是否撞到自己
            for (let segment of snake) {
                if (head.x === segment.x && head.y === segment.y) {
                    console.log('💥 撞到自己了！游戏结束');
                    gameOver();
                    return;
                }
            }

            snake.unshift(head);

            // 检查是否吃到普通食物
            if (head.x === food.x && head.y === food.y) {
                const points = Math.floor(10 * scoreMultiplier);
                score += points;
                console.log(`🍎 吃到普通食物！得分 +${points}`);
                scoreElement.textContent = score;
                lengthElement.textContent = snake.length;
                
                // 播放吃食物音效
                playSound(800, 0.1);
                
                // 检查升级
                checkLevelUp();
                
                generateFood();
                generateSpecialFood();
            } else if (specialFood && head.x === specialFood.x && head.y === specialFood.y) {
                // 吃到特殊食物
                if (specialFood.type === 'speed') {
                    console.log('⚡ 吃到加速食物！');
                    activatePowerUp();
                    playSound(1200, 0.2, 'square');
                } else {
                    const bonusPoints = Math.floor(50 * scoreMultiplier);
                    score += bonusPoints;
                    console.log(`⭐ 吃到奖励食物！得分 +${bonusPoints}`);
                    playSound(1000, 0.3, 'triangle');
                }
                scoreElement.textContent = score;
                lengthElement.textContent = snake.length;
                specialFood = null;
                checkLevelUp();
            } else {
                snake.pop();
            }

            drawGame();
        }

        // 激活能力提升
        function activatePowerUp() {
            powerUpActive = true;
            powerUpTimeLeft = 5;
            powerUpIndicator.style.display = 'block';
            
            // 更新游戏速度
            clearInterval(gameLoop);
            gameLoop = setInterval(moveSnake, Math.max(currentSpeed / 2, 30));
            
            // 倒计时
            powerUpInterval = setInterval(() => {
                powerUpTimeLeft--;
                powerUpTimer.textContent = powerUpTimeLeft;
                
                if (powerUpTimeLeft <= 0) {
                    deactivatePowerUp();
                }
            }, 1000);
        }

        // 取消能力提升
        function deactivatePowerUp() {
            powerUpActive = false;
            powerUpIndicator.style.display = 'none';
            clearInterval(powerUpInterval);
            
            // 恢复正常速度
            clearInterval(gameLoop);
            gameLoop = setInterval(moveSnake, currentSpeed);
            console.log('⚡ 加速效果结束');
        }

        // 检查升级
        function checkLevelUp() {
            const newLevel = Math.floor(score / 100) + 1;
            if (newLevel > level) {
                level = newLevel;
                levelElement.textContent = level;
                console.log(`🎉 升级到等级 ${level}！`);
                
                // 播放升级音效
                playSound(600, 0.5, 'sawtooth');
                
                // 稍微增加游戏速度
                currentSpeed = Math.max(currentSpeed - 5, 50);
                if (gameRunning && !powerUpActive) {
                    clearInterval(gameLoop);
                    gameLoop = setInterval(moveSnake, currentSpeed);
                }
            }
        }

        // 开始游戏
        function startGame() {
            if (gameRunning) return;
            
            console.log('🚀 开始新游戏');
            gameRunning = true;
            gamePaused = false;
            gameOverScreen.style.display = 'none';
            
            // 重置游戏状态
            snake = [{ x: 10, y: 10 }];
            dx = 0;
            dy = 0;
            score = 0;
            level = 1;
            powerUpActive = false;
            specialFood = null;
            
            // 更新显示
            scoreElement.textContent = score;
            lengthElement.textContent = snake.length;
            levelElement.textContent = level;
            powerUpIndicator.style.display = 'none';
            
            updateDifficulty();
            generateFood();
            generateSpecialFood();
            drawGame();
            
            gameLoop = setInterval(moveSnake, currentSpeed);
            
            // 播放开始音效
            playSound(440, 0.3);
        }

        // 暂停游戏
        function pauseGame() {
            if (!gameRunning) return;
            
            gamePaused = !gamePaused;
            console.log(gamePaused ? '⏸️ 游戏暂停' : '▶️ 游戏继续');
            
            if (gamePaused) {
                clearInterval(gameLoop);
                if (powerUpActive) {
                    clearInterval(powerUpInterval);
                }
            } else {
                const speed = powerUpActive ? Math.max(currentSpeed / 2, 30) : currentSpeed;
                gameLoop = setInterval(moveSnake, speed);
                if (powerUpActive) {
                    powerUpInterval = setInterval(() => {
                        powerUpTimeLeft--;
                        powerUpTimer.textContent = powerUpTimeLeft;
                        if (powerUpTimeLeft <= 0) {
                            deactivatePowerUp();
                        }
                    }, 1000);
                }
            }
        }

        // 重新开始游戏
        function resetGame() {
            console.log('🔄 重置游戏');
            gameRunning = false;
            gamePaused = false;
            clearInterval(gameLoop);
            clearInterval(powerUpInterval);
            gameOverScreen.style.display = 'none';
            deactivatePowerUp();
            startGame();
        }

        // 游戏结束
        function gameOver() {
            console.log(`💀 游戏结束！最终得分: ${score}, 长度: ${snake.length}, 等级: ${level}`);
            gameRunning = false;
            clearInterval(gameLoop);
            clearInterval(powerUpInterval);
            deactivatePowerUp();
            
            finalScoreElement.textContent = score;
            finalLengthElement.textContent = snake.length;
            finalLevelElement.textContent = level;
            
            // 检查是否创造新纪录
            if (score > highScore) {
                highScore = score;
                highScoreElement.textContent = highScore;
                localStorage.setItem('enhancedSnakeHighScore', highScore);
                newRecordElement.style.display = 'block';
                console.log('🏆 新纪录！');
                
                // 播放新纪录音效
                playSound(880, 1, 'triangle');
            } else {
                newRecordElement.style.display = 'none';
                // 播放游戏结束音效
                playSound(220, 0.8, 'sawtooth');
            }
            
            gameOverScreen.style.display = 'flex';
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning || gamePaused) return;

            switch(e.key) {
                case 'ArrowUp':
                    if (dy !== 1) {
                        dx = 0;
                        dy = -1;
                    }
                    break;
                case 'ArrowDown':
                    if (dy !== -1) {
                        dx = 0;
                        dy = 1;
                    }
                    break;
                case 'ArrowLeft':
                    if (dx !== 1) {
                        dx = -1;
                        dy = 0;
                    }
                    break;
                case 'ArrowRight':
                    if (dx !== -1) {
                        dx = 1;
                        dy = 0;
                    }
                    break;
                case ' ':
                    e.preventDefault();
                    pauseGame();
                    break;
            }
        });

        // 按钮控制
        document.getElementById('upBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (dy !== 1) {
                dx = 0;
                dy = -1;
            }
        });

        document.getElementById('downBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (dy !== -1) {
                dx = 0;
                dy = 1;
            }
        });

        document.getElementById('leftBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (dx !== 1) {
                dx = -1;
                dy = 0;
            }
        });

        document.getElementById('rightBtn').addEventListener('click', () => {
            if (!gameRunning || gamePaused) return;
            if (dx !== -1) {
                dx = 1;
                dy = 0;
            }
        });

        // 难度选择事件
        difficultySelect.addEventListener('change', () => {
            if (!gameRunning) {
                updateDifficulty();
            }
        });

        // 初始化游戏
        initGame();
    </script>
</body>
</html>