<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文生图应用</title>
    <style>
        /* 酷黑主题样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #121212;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #1e1e1e;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        h1 {
            text-align: center;
            color: #bb86fc;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #cf6679;
        }

        textarea, select, button {
            width: 100%;
            padding: 12px;
            border-radius: 5px;
            border: 1px solid #333;
            background-color: #2d2d2d;
            color: #e0e0e0;
            font-size: 16px;
            box-sizing: border-box;
        }

        textarea {
            min-height: 100px;
            resize: vertical;
        }

        button {
            background-color: #bb86fc;
            color: #121212;
            border: none;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin-top: 10px;
        }

        button:hover {
            background-color: #9a67ea;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        button:hover {
            background-color: #9a67ea;
        }

        button:disabled {
            background-color: #444;
            cursor: not-allowed;
        }

        .dimensions {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 8px;
            margin-top: 10px;
        }

        .dimension-option {
            padding: 12px;
            background-color: #2d2d2d;
            border: 1px solid #333;
            border-radius: 5px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .dimension-option:hover {
            background-color: #3d3d3d;
            border-color: #bb86fc;
        }

        .dimension-option.selected {
            background-color: #bb86fc;
            color: #121212;
            border-color: #bb86fc;
        }

        /* 加载动画 */
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #bb86fc;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            display: none;
            margin: 10px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 下载按钮 */
        .download-btn {
            background-color: #bb86fc;
            color: #121212;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            margin-top: 10px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease, transform 0.2s;
        }

        .download-btn:hover {
            background-color: #3700b3;
            color: white;
            transform: translateY(-2px);
        }

        .download-btn:active {
            transform: translateY(0);
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background-color: #2d2d2d;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease, transform 0.3s ease;
            transform: translateY(-10px);
        }

        .status.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        .status.show {
            display: block;
        }

        .status.loading {
            border-left: 4px solid #bb86fc;
        }

        .status.error {
            border-left: 4px solid #cf6679;
        }

        .status.success {
            border-left: 4px solid #4caf50;
        }

        .result-container {
            margin-top: 30px;
        }

        .result-title {
            font-size: 1.5em;
            color: #bb86fc;
            margin-bottom: 15px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }

        /* 媒体查询：小屏幕设备 */
        @media (max-width: 600px) {
            .container {
                padding: 15px;
            }

            h1 {
                font-size: 2em;
            }

            .dimensions {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 5px;
            }

            .image-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }

        .image-item {
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .image-item img {
            width: 100%;
            height: auto;
            display: block;
        }

        .character-count {
            text-align: right;
            font-size: 0.9em;
            color: #aaa;
            margin-top: 5px;
        }

        .character-count.warn {
            color: #cf6679;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文生图应用</h1>
        
        <div class="input-group">
            <label for="prompt">图片描述</label>
            <textarea id="prompt" placeholder="请输入您想要生成的图片描述..."></textarea>
            <div class="character-count" id="characterCount">0 / 200</div>
        </div>
        
        <div class="input-group">
            <label>图片尺寸</label>
            <div class="dimensions" id="dimensions">
                <div class="dimension-option" data-width="512" data-height="512">512×512</div>
                <div class="dimension-option" data-width="640" data-height="360">640×360</div>
                <div class="dimension-option" data-width="360" data-height="640">360×640</div>
                <div class="dimension-option" data-width="1024" data-height="1024">1024×1024</div>
                <div class="dimension-option" data-width="1280" data-height="720">1280×720</div>
                <div class="dimension-option" data-width="720" data-height="1280">720×1280</div>
            </div>
        </div>
        
        <button id="generateBtn">生成图片</button>
        
        <div class="status" id="status"></div>
        
        <!-- 加载动画 -->
        <div class="spinner" id="spinner"></div>
    
        <div class="result-container">
            <div class="result-title">生成结果</div>
            <div class="image-grid" id="imageGrid"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedWidth = 512;
        let selectedHeight = 512;
        let taskId = null;
        let pollInterval = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log("文生图应用界面已加载");
            initializeApp();
        });

        // 初始化应用
        function initializeApp() {
            // 绑定事件监听器
            document.getElementById('prompt').addEventListener('input', handlePromptInput);
            document.getElementById('generateBtn').addEventListener('click', generateImage);
            
            // 为尺寸选项添加点击事件
            const dimensionOptions = document.querySelectorAll('.dimension-option');
            dimensionOptions.forEach(option => {
                option.addEventListener('click', selectDimension);
            });
            
            // 默认选中第一个尺寸选项
            if (dimensionOptions.length > 0) {
                dimensionOptions[0].classList.add('selected');
            }
        }

        // 处理文本输入
        function handlePromptInput(event) {
            const prompt = event.target.value;
            const charCount = prompt.length;
            const charCountElement = document.getElementById('characterCount');
            
            // 更新字符计数
            charCountElement.textContent = `${charCount} / 200`;
            
            // 如果超过限制，添加警告样式
            if (charCount > 200) {
                charCountElement.classList.add('warn');
            } else {
                charCountElement.classList.remove('warn');
            }
        }

        // 选择图片尺寸
        function selectDimension(event) {
            // 移除所有选项的选中状态
            const dimensionOptions = document.querySelectorAll('.dimension-option');
            dimensionOptions.forEach(option => {
                option.classList.remove('selected');
            });
            
            // 为当前点击的选项添加选中状态
            const selectedOption = event.target;
            selectedOption.classList.add('selected');
            
            // 获取选中的尺寸
            selectedWidth = parseInt(selectedOption.dataset.width);
            selectedHeight = parseInt(selectedOption.dataset.height);
            
            console.log(`选中的尺寸: ${selectedWidth}×${selectedHeight}`);
        }

        // 生成图片
        async function generateImage() {
            // 获取用户输入的文本描述
            const prompt = document.getElementById('prompt').value.trim();
            
            // 验证输入
            if (!prompt) {
                showStatus('请输入图片描述内容', 'error');
                return;
            }

            if (prompt.length > 200) {
                showStatus('图片描述不能超过200个字符，请精简描述内容', 'error');
                return;
            }
            
            // 禁用生成按钮，防止重复点击
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = true;
            generateBtn.textContent = '生成中...';
            
            // 显示加载状态
            showStatus('正在提交生成请求，请稍候...', 'loading');
            
            try {
                // 调用API提交请求
                console.log('正在提交生成请求...');
                const response = await fetch('https://joycode-api.jd.com/api/saas/tool/v1/plugin/run/39041', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'lwrs4h6ng7CqcTFAnl7E3C6jlw6fMAp9K4N9nh2zMkA='
                    },
                    body: JSON.stringify({
                        params: {
                            prompt: prompt,
                            width: selectedWidth,
                            height: selectedHeight,
                            image_num: 1 // 默认生成一张图片
                        }
                    })
                });
                
                // 检查响应状态
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // 解析响应数据
                const result = await response.json();
                console.log('API响应:', result);
                
                // 检查API返回的状态码
                if (result.code !== 200) {
                    throw new Error(result.msg || 'API请求失败');
                }
                
                // 获取任务ID
                taskId = result.data.data.task_id;
                console.log('任务ID:', taskId);
                
                // 显示成功状态
                showStatus('请求已提交，正在生成图片...', 'loading');
                
                // 开始轮询查询结果
                startPolling();
            } catch (error) {
                console.error('生成图片时出错:', error);
                showStatus(`生成图片失败: ${error.message}`, 'error');
                
                // 重新启用生成按钮
                generateBtn.disabled = false;
                generateBtn.textContent = '生成图片';
            }
        }

        // 开始轮询查询结果
        function startPolling() {
            // 清除之前的轮询（如果有的话）
            if (pollInterval) {
                clearInterval(pollInterval);
            }
            
            // 每3秒查询一次结果
            pollInterval = setInterval(checkResult, 3000);
            console.log('开始轮询查询结果...');
        }

        // 查询生成结果
        async function checkResult() {
            if (!taskId) {
                console.error('任务ID不存在');
                clearInterval(pollInterval);
                return;
            }
            
            try {
                // 调用API查询结果
                console.log('正在查询生成结果...');
                const response = await fetch('https://joycode-api.jd.com/api/saas/tool/v1/plugin/run/39044', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'lwrs4h6ng7CqcTFAnl7E3C6jlw6fMAp9K4N9nh2zMkA='
                    },
                    body: JSON.stringify({
                        params: {
                            task_id: taskId
                        }
                    })
                });
                
                // 检查响应状态
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // 解析响应数据
                const result = await response.json();
                console.log('查询结果响应:', result);
                
                // 检查API返回的状态码
                if (result.code !== 200) {
                    throw new Error(result.msg || '查询结果失败');
                }
                
                // 获取任务状态
                const taskData = result.data.data;
                const subTaskResultList = taskData.sub_task_result_list;
                
                // 检查子任务状态
                if (subTaskResultList && subTaskResultList.length > 0) {
                    const subTask = subTaskResultList[0];
                    const subTaskStatus = subTask.sub_task_status;
                    
                    console.log('子任务状态:', subTaskStatus);
                    
                    // 根据状态处理
                    if (subTaskStatus === 'SUCCESS') {
                        // 生成成功，显示图片
                        clearInterval(pollInterval);
                        showStatus('图片生成成功！', 'success');
                        displayImages(subTask.final_image_list);
                        
                        // 重新启用生成按钮
                        const generateBtn = document.getElementById('generateBtn');
                        generateBtn.disabled = false;
                        generateBtn.textContent = '生成图片';
                    } else if (subTaskStatus === 'RUNNING') {
                        // 仍在生成中，继续轮询
                        showStatus('图片生成中，AI正在努力创作，请稍候...', 'loading');
                    } else {
                        // 其他状态（如失败）
                        console.warn('任务状态:', subTaskStatus);
                        showStatus(`生成失败，状态：${subTaskStatus}`, 'error');
                    }
                }
            } catch (error) {
                console.error('查询结果时出错:', error);
                showStatus(`查询结果失败: ${error.message}`, 'error');
                
                // 停止轮询
                clearInterval(pollInterval);
                
                // 重新启用生成按钮
                const generateBtn = document.getElementById('generateBtn');
                generateBtn.disabled = false;
                generateBtn.textContent = '生成图片';
            }
        }

        // 显示状态信息
        function showStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = 'status show ' + type;
            console.log(`状态 [${type}]: ${message}`);
            
            // 控制加载动画的显示
            const spinner = document.getElementById('spinner');
            if (type === 'loading') {
                spinner.style.display = 'block';
            } else {
                spinner.style.display = 'none';
            }
        }

        // 显示生成的图片
        function displayImages(imageList) {
            const imageGrid = document.getElementById('imageGrid');
            imageGrid.innerHTML = ''; // 清空之前的内容

            if (imageList && imageList.length > 0) {
                imageList.forEach((image, index) => {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';

                    const img = document.createElement('img');
                    img.src = image.img_url;
                    img.alt = '生成的图片';
                    img.onload = function() {
                        console.log('图片加载成功:', image.img_url);
                    };
                    img.onerror = function() {
                        console.error('图片加载失败:', image.img_url);
                        showStatus('部分图片加载失败', 'error');
                    };

                    // 添加下载按钮
                    const downloadBtn = document.createElement('button');
                    downloadBtn.className = 'download-btn';
                    downloadBtn.textContent = '下载图片';
                    downloadBtn.onclick = function() {
                        downloadImage(image.img_url, `generated-image-${index + 1}.png`);
                    };

                    imageItem.appendChild(img);
                    imageItem.appendChild(downloadBtn);
                    imageGrid.appendChild(imageItem);
                });
            } else {
                showStatus('未找到生成的图片', 'error');
            }
        }

        // 下载图片
        function downloadImage(url, filename) {
            showStatus('正在准备下载...', 'loading');
            
            fetch(url)
                .then(response => response.blob())
                .then(blob => {
                    const blobUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = blobUrl;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(blobUrl);
                    showStatus('图片下载成功！', 'success');
                })
                .catch(error => {
                    console.error('下载图片失败:', error);
                    showStatus('图片下载失败: ' + error.message, 'error');
                });
        }
    </script>
</body>
</html>